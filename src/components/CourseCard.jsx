import React, { memo, useCallback } from 'react'
import { useNavigate } from 'react-router-dom'

const CourseCard = memo(({ course, index }) => {
  const navigate = useNavigate()

  const handleClick = useCallback(() => {
      window.open(`/course/${course.id}`, '_blank');
  }, [navigate, course.id])

  // 获取主题颜色配置
  const getThemeColors = (theme) => {
      const themes = {
          blue: {
              primary: 'bg-blue-600',
              light: 'bg-blue-50',
              border: 'border-blue-600',
              text: 'text-blue-600',
              hover: 'group-hover:border-blue-300'
          },
          purple: {
              primary: 'bg-purple-600',
              light: 'bg-purple-50',
              border: 'border-purple-600',
              text: 'text-purple-600',
              hover: 'group-hover:border-purple-300'
          },
          green: {
              primary: 'bg-green-600',
              light: 'bg-green-50',
              border: 'border-green-600',
              text: 'text-green-600',
              hover: 'group-hover:border-green-300'
          },
          orange: {
              primary: 'bg-orange-600',
              light: 'bg-orange-50',
              border: 'border-orange-600',
              text: 'text-orange-600',
              hover: 'group-hover:border-orange-300'
          },
          red: {
              primary: 'bg-red-600',
              light: 'bg-red-50',
              border: 'border-red-600',
              text: 'text-red-600',
              hover: 'group-hover:border-red-300'
          },
          teal: {
              primary: 'bg-teal-600',
              light: 'bg-teal-50',
              border: 'border-teal-600',
              text: 'text-teal-600',
              hover: 'group-hover:border-teal-300'
          },
          pink: {
              primary: 'bg-pink-600',
              light: 'bg-pink-50',
              border: 'border-pink-600',
              text: 'text-pink-600',
              hover: 'group-hover:border-pink-300'
          },
          indigo: {
              primary: 'bg-indigo-600',
              light: 'bg-indigo-50',
              border: 'border-indigo-600',
              text: 'text-indigo-600',
              hover: 'group-hover:border-indigo-300'
          },
          slate: {
              primary: 'bg-slate-600',
              light: 'bg-slate-50',
              border: 'border-slate-600',
              text: 'text-slate-600',
              hover: 'group-hover:border-slate-300'
          },
          olive: {
              primary: 'bg-olive-600',
              light: 'bg-olive-50',
              border: 'border-olive-600',
              text: 'text-olive-600',
              hover: 'group-hover:border-olive-300'
          },
          taupe: {
              primary: 'bg-taupe-600',
              light: 'bg-taupe-50',
              border: 'border-taupe-600',
              text: 'text-taupe-600',
              hover: 'group-hover:border-taupe-300'
          },
          mauve: {
              primary: 'bg-mauve-600',
              light: 'bg-mauve-50',
              border: 'border-mauve-600',
              text: 'text-mauve-600',
              hover: 'group-hover:border-mauve-300'
          }
      }
    return themes[theme] || themes.blue
  }

  const themeColors = getThemeColors(course.theme)

  return (
    <div
      className="group cursor-pointer slide-up hover-lift"
      onClick={handleClick}
      style={{ animationDelay: `${index * 50}ms` }}
    >
      <div className="solid-card p-5 h-full hover:shadow-lg transition-all duration-300">
        {/* 课程封面 */}
        <div className={`relative mb-4 overflow-hidden rounded-lg border ${themeColors.border}`}>
          <div className={`aspect-video ${themeColors.primary} flex items-center justify-center relative`}>
            {/* 背景图片或渐变 */}
            <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-black/20"></div>

            {/* 课程图标 */}
            <div className="relative z-10 text-center">
              <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mb-3 mx-auto backdrop-blur-sm">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
              </div>
              <div className="text-white text-sm font-semibold opacity-90">
                {course.title}
              </div>
            </div>
          </div>

          {/* 等级标识 */}
          <div className="absolute top-3 right-3">
            <div className={`px-3 py-1 bg-white/90 backdrop-blur-sm rounded-full text-xs font-semibold ${themeColors.text} shadow-sm`}>
              {course.level}
            </div>
          </div>

          {/* 装饰性元素 */}
          <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-white/30 to-transparent"></div>
        </div>

        {/* 课程标题 */}
        <h3 className="text-lg font-bold text-slate-800 mb-3">
          {course.title}
        </h3>

        {/* 课程描述 */}
        <p className="text-slate-600 text-sm mb-6 line-clamp-2 leading-relaxed">
          {course.description}
        </p>

        {/* 章节预览 - 左右结构，卡片效果 */}
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            {course.chapters.slice(0, 3).map((chapter, idx) => (
              <div key={chapter.id} className="flex items-center text-xs text-slate-600 bg-slate-50 hover:bg-slate-100 p-2 rounded-lg transition-colors duration-200">
                <div className={`w-5 h-5 rounded ${themeColors.light} flex items-center justify-center mr-3 text-xs font-bold ${themeColors.text}`}>
                  {idx + 1}
                </div>
                <span className="truncate font-medium">{chapter.title}</span>
              </div>
            ))}
          </div>
          <div className="space-y-2">
            {course.chapters.slice(3, 6).map((chapter, idx) => (
              <div key={chapter.id} className="flex items-center text-xs text-slate-600 bg-slate-50 hover:bg-slate-100 p-2 rounded-lg transition-colors duration-200">
                <div className={`w-5 h-5 rounded ${themeColors.light} flex items-center justify-center mr-3 text-xs font-bold ${themeColors.text}`}>
                  {idx + 4}
                </div>
                <span className="truncate font-medium">{chapter.title}</span>
              </div>
            ))}

          </div>
        </div>
      </div>
    </div>
  )
})

CourseCard.displayName = 'CourseCard'

export default CourseCard
